Dependencies for Project 'Project', Target 'McuSTUDIO_F470VET6': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\USER\src\gd32f4xx_it.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_it.o --omf_browse .\output\gd32f4xx_it.crf --depend .\output\gd32f4xx_it.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\USER\inc\gd32f4xx_it.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\main.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\USER\src\main.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\main.o --omf_browse .\output\main.crf --depend .\output\main.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\USER\src\systick.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\systick.o --omf_browse .\output\systick.crf --depend .\output\systick.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
F (..\Components\bsp\mcu_cmic_gd32f470vet6.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\mcu_cmic_gd32f470vet6.o --omf_browse .\output\mcu_cmic_gd32f470vet6.crf --depend .\output\mcu_cmic_gd32f470vet6.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)()
F (..\Components\gd25qxx\gd25qxx.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd25qxx.o --omf_browse .\output\gd25qxx.crf --depend .\output\gd25qxx.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\Components\gd25qxx\lfs.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\lfs.o --omf_browse .\output\lfs.crf --depend .\output\lfs.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\gd25qxx\lfs.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (..\Components\gd25qxx\lfs_util.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\inttypes.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\assert.h)(0x68345B0E)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\Components\gd25qxx\lfs_port.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\lfs_port.o --omf_browse .\output\lfs_port.crf --depend .\output\lfs_port.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\gd25qxx\lfs_port.h)(0x6846DD9D)
I (..\Components\gd25qxx\lfs.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\Components\gd25qxx\lfs_util.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\lfs_util.o --omf_browse .\output\lfs_util.crf --depend .\output\lfs_util.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\gd25qxx\lfs_util.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\inttypes.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\assert.h)(0x68345B0E)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\Components\oled\oled.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\oled.o --omf_browse .\output\oled.crf --depend .\output\oled.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\Components\oled\oledfont.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
F (..\Components\ebtn\bit_array.h)(0x6846DD9D)()
F (..\Components\ebtn\ebtn.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\ebtn.o --omf_browse .\output\ebtn.crf --depend .\output\ebtn.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
F (..\Components\ebtn\ebtn.h)(0x6846DD9D)()
F (..\Components\sdio\sdio_sdcard.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\sdio_sdcard.o --omf_browse .\output\sdio_sdcard.crf --depend .\output\sdio_sdcard.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
F (..\Components\fatfs\ff.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\ff.o --omf_browse .\output\ff.crf --depend .\output\ff.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
F (..\Components\fatfs\diskio.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\diskio.o --omf_browse .\output\diskio.crf --depend .\output\diskio.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\btn_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\btn_app.o --omf_browse .\output\btn_app.crf --depend .\output\btn_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\led_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\led_app.o --omf_browse .\output\led_app.crf --depend .\output\led_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\oled_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\oled_app.o --omf_browse .\output\oled_app.crf --depend .\output\oled_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\scheduler.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\scheduler.o --omf_browse .\output\scheduler.crf --depend .\output\scheduler.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\usart_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\usart_app.o --omf_browse .\output\usart_app.crf --depend .\output\usart_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\sd_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\sd_app.o --omf_browse .\output\sd_app.crf --depend .\output\sd_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\rtc_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\rtc_app.o --omf_browse .\output\rtc_app.crf --depend .\output\rtc_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\APP\rtc_app.h)(0x6846DD9D)()
F (..\APP\adc_app.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\adc_app.o --omf_browse .\output\adc_app.crf --depend .\output\adc_app.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\USER\inc\systick.h)(0x6846DD9D)
I (..\Components\ebtn\ebtn.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Components\ebtn\bit_array.h)(0x6846DD9D)
I (..\Components\oled\oled.h)(0x6846DD9D)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846DD9D)
I (..\Components\sdio\sdio_sdcard.h)(0x6846DD9D)
I (..\Components\fatfs\ff.h)(0x6846DD9D)
I (..\Components\fatfs\integer.h)(0x6846DD9D)
I (..\Components\fatfs\ffconf.h)(0x6846DD9D)
I (..\Components\fatfs\diskio.h)(0x6846DD9D)
I (..\APP\sd_app.h)(0x6846DD9D)
I (..\APP\led_app.h)(0x6846DD9D)
I (..\APP\adc_app.h)(0x6846DD9D)
I (..\APP\oled_app.h)(0x6846DD9D)
I (..\APP\usart_app.h)(0x6846DD9D)
I (..\APP\rtc_app.h)(0x6846DD9D)
I (..\APP\btn_app.h)(0x6846DD9D)
I (..\APP\scheduler.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\Libraries\Source\gd32f4xx_adc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_adc.o --omf_browse .\output\gd32f4xx_adc.crf --depend .\output\gd32f4xx_adc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_can.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_can.o --omf_browse .\output\gd32f4xx_can.crf --depend .\output\gd32f4xx_can.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_crc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_crc.o --omf_browse .\output\gd32f4xx_crc.crf --depend .\output\gd32f4xx_crc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_ctc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_ctc.o --omf_browse .\output\gd32f4xx_ctc.crf --depend .\output\gd32f4xx_ctc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_dac.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_dac.o --omf_browse .\output\gd32f4xx_dac.crf --depend .\output\gd32f4xx_dac.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_dbg.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_dbg.o --omf_browse .\output\gd32f4xx_dbg.crf --depend .\output\gd32f4xx_dbg.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_dci.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_dci.o --omf_browse .\output\gd32f4xx_dci.crf --depend .\output\gd32f4xx_dci.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_dma.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_dma.o --omf_browse .\output\gd32f4xx_dma.crf --depend .\output\gd32f4xx_dma.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_enet.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_enet.o --omf_browse .\output\gd32f4xx_enet.crf --depend .\output\gd32f4xx_enet.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
F (..\Libraries\Source\gd32f4xx_exmc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_exmc.o --omf_browse .\output\gd32f4xx_exmc.crf --depend .\output\gd32f4xx_exmc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_exti.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_exti.o --omf_browse .\output\gd32f4xx_exti.crf --depend .\output\gd32f4xx_exti.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_fmc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_fmc.o --omf_browse .\output\gd32f4xx_fmc.crf --depend .\output\gd32f4xx_fmc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_fwdgt.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_fwdgt.o --omf_browse .\output\gd32f4xx_fwdgt.crf --depend .\output\gd32f4xx_fwdgt.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_gpio.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_gpio.o --omf_browse .\output\gd32f4xx_gpio.crf --depend .\output\gd32f4xx_gpio.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_i2c.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_i2c.o --omf_browse .\output\gd32f4xx_i2c.crf --depend .\output\gd32f4xx_i2c.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_ipa.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_ipa.o --omf_browse .\output\gd32f4xx_ipa.crf --depend .\output\gd32f4xx_ipa.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_iref.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_iref.o --omf_browse .\output\gd32f4xx_iref.crf --depend .\output\gd32f4xx_iref.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_misc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_misc.o --omf_browse .\output\gd32f4xx_misc.crf --depend .\output\gd32f4xx_misc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_pmu.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_pmu.o --omf_browse .\output\gd32f4xx_pmu.crf --depend .\output\gd32f4xx_pmu.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_rcu.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_rcu.o --omf_browse .\output\gd32f4xx_rcu.crf --depend .\output\gd32f4xx_rcu.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_rtc.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_rtc.o --omf_browse .\output\gd32f4xx_rtc.crf --depend .\output\gd32f4xx_rtc.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_sdio.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_sdio.o --omf_browse .\output\gd32f4xx_sdio.crf --depend .\output\gd32f4xx_sdio.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_spi.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_spi.o --omf_browse .\output\gd32f4xx_spi.crf --depend .\output\gd32f4xx_spi.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_syscfg.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_syscfg.o --omf_browse .\output\gd32f4xx_syscfg.crf --depend .\output\gd32f4xx_syscfg.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_timer.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_timer.o --omf_browse .\output\gd32f4xx_timer.crf --depend .\output\gd32f4xx_timer.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_tli.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_tli.o --omf_browse .\output\gd32f4xx_tli.crf --depend .\output\gd32f4xx_tli.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_trng.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_trng.o --omf_browse .\output\gd32f4xx_trng.crf --depend .\output\gd32f4xx_trng.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_usart.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_usart.o --omf_browse .\output\gd32f4xx_usart.crf --depend .\output\gd32f4xx_usart.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\Source\gd32f4xx_wwdgt.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\gd32f4xx_wwdgt.o --omf_browse .\output\gd32f4xx_wwdgt.crf --depend .\output\gd32f4xx_wwdgt.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\Libraries\startup_gd32f450_470.s)(0x6846DD9D)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

--pd "__UVISION_VERSION SETA 533" --pd "_RTE_ SETA 1" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\list\startup_gd32f450_470.lst --xref -o .\output\startup_gd32f450_470.o --depend .\output\startup_gd32f450_470.d)
F (..\Driver\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x6846DD9D)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\system_gd32f4xx.o --omf_browse .\output\system_gd32f4xx.crf --depend .\output\system_gd32f4xx.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x5E8ED122)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846DD9D)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846DD9D)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846DD9D)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846DD9D)
F (..\readme.txt)(0x00000000)()
F (..\io_multiplexing_table.txt)(0x6846DD9D)()
F (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.c)(0x6755820C)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\perf_counter.o --omf_browse .\output\perf_counter.crf --depend .\output\perf_counter.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
F (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.c)(0x6755820C)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Libraries\Include -I ..\USER\inc -I ..\Driver\CMSIS\GD\GD32F4xx\Include -I ..\Components\bsp -I ..\Components\oled -I ..\Components\gd25qxx -I ..\Components\ebtn -I ..\Components\sdio -I ..\Components\fatfs -I ..\APP

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\output\perfc_port_default.o --omf_browse .\output\perfc_port_default.crf --depend .\output\perfc_port_default.d)
I (.\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x684C0318)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stdbool.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x5E8306C2)
I (D:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x5E8ED122)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.h)(0x6755820C)
I (D:\keil-MDK\Keil_MDK\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h)(0x6755820C)
F (D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\systick_wrapper_ual.s)(0x6755820C)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-I.\RTE\_McuSTUDIO_F470VET6

-ID:\keil-MDK\Keil_MDK\PACK\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\keil-MDK\Keil_MDK\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0

--pd "__UVISION_VERSION SETA 533" --pd "_RTE_ SETA 1" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\list\systick_wrapper_ual.lst --xref -o .\output\systick_wrapper_ual.o --depend .\output\systick_wrapper_ual.d)
