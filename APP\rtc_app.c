#include "mcu_cmic_gd32f470vet6.h"

extern rtc_parameter_struct rtc_initpara;

/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    rtc_current_time_get(&rtc_initpara);

    oled_printf(0, 3, "%0.2x:%0.2x:%0.2x", rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/*!
    \brief      get current timestamp (seconds since epoch-like)
    \param[in]  none
    \param[out] none
    \retval     timestamp value
*/
uint32_t rtc_get_timestamp(void)
{
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);

    // 简单的时间戳计算：小时*3600 + 分钟*60 + 秒
    // 注意：这里使用BCD格式，需要转换为十进制
    uint32_t hour = ((current_time.hour >> 4) & 0x0F) * 10 + (current_time.hour & 0x0F);
    uint32_t minute = ((current_time.minute >> 4) & 0x0F) * 10 + (current_time.minute & 0x0F);
    uint32_t second = ((current_time.second >> 4) & 0x0F) * 10 + (current_time.second & 0x0F);

    return hour * 3600 + minute * 60 + second;
}

/*!
    \brief      convert timestamp to string format
    \param[in]  timestamp: timestamp value
    \param[in]  str_buffer: output string buffer
    \param[in]  buffer_size: buffer size
    \param[out] none
    \retval     none
*/
void rtc_timestamp_to_string(uint32_t timestamp, char* str_buffer, uint16_t buffer_size)
{
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);

    // 将BCD格式转换为字符串：HH:MM:SS
    snprintf(str_buffer, buffer_size, "%02x:%02x:%02x",
             current_time.hour, current_time.minute, current_time.second);
}
