{"name": "Project", "target": "McuSTUDIO_F470VET6", "toolchain": "AC5", "toolchainLocation": "D:\\keil-MDK\\Keil_MDK\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.21.3\\res\\data\\models\\win32/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 32, "rootDir": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "dumpPath": "build\\McuSTUDIO_F470VET6", "outDir": "build\\McuSTUDIO_F470VET6", "ram": 196608, "rom": 524288, "incDirs": ["../Libraries/Include", "../USER/inc", "../Driver/CMSIS/GD/GD32F4xx/Include", "../Components/bsp", "../Components/oled", "../Components/gd25qxx", "../Components/ebtn", "../Components/sdio", "../Components/fatfs", "../APP", ".cmsis/include", "../MDK/RTE/_McuSTUDIO_F470VET6"], "libDirs": [], "defines": ["USE_STDPERIPH_DRIVER", "GD32F470"], "sourceList": ["../APP/adc_app.c", "../APP/btn_app.c", "../APP/led_app.c", "../APP/oled_app.c", "../APP/rtc_app.c", "../APP/scheduler.c", "../APP/sd_app.c", "../APP/usart_app.c", "../Components/bsp/mcu_cmic_gd32f470vet6.c", "../Components/ebtn/ebtn.c", "../Components/fatfs/diskio.c", "../Components/fatfs/ff.c", "../Components/gd25qxx/gd25qxx.c", "../Components/gd25qxx/lfs.c", "../Components/gd25qxx/lfs_port.c", "../Components/gd25qxx/lfs_util.c", "../Components/oled/oled.c", "../Components/sdio/sdio_sdcard.c", "../Driver/CMSIS/GD/GD32F4xx/Source/system_gd32f4xx.c", "../Libraries/Source/gd32f4xx_adc.c", "../Libraries/Source/gd32f4xx_can.c", "../Libraries/Source/gd32f4xx_crc.c", "../Libraries/Source/gd32f4xx_ctc.c", "../Libraries/Source/gd32f4xx_dac.c", "../Libraries/Source/gd32f4xx_dbg.c", "../Libraries/Source/gd32f4xx_dci.c", "../Libraries/Source/gd32f4xx_dma.c", "../Libraries/Source/gd32f4xx_enet.c", "../Libraries/Source/gd32f4xx_exmc.c", "../Libraries/Source/gd32f4xx_exti.c", "../Libraries/Source/gd32f4xx_fmc.c", "../Libraries/Source/gd32f4xx_fwdgt.c", "../Libraries/Source/gd32f4xx_gpio.c", "../Libraries/Source/gd32f4xx_i2c.c", "../Libraries/Source/gd32f4xx_ipa.c", "../Libraries/Source/gd32f4xx_iref.c", "../Libraries/Source/gd32f4xx_misc.c", "../Libraries/Source/gd32f4xx_pmu.c", "../Libraries/Source/gd32f4xx_rcu.c", "../Libraries/Source/gd32f4xx_rtc.c", "../Libraries/Source/gd32f4xx_sdio.c", "../Libraries/Source/gd32f4xx_spi.c", "../Libraries/Source/gd32f4xx_syscfg.c", "../Libraries/Source/gd32f4xx_timer.c", "../Libraries/Source/gd32f4xx_tli.c", "../Libraries/Source/gd32f4xx_trng.c", "../Libraries/Source/gd32f4xx_usart.c", "../Libraries/Source/gd32f4xx_wwdgt.c", "../Libraries/startup_gd32f450_470.s", "../USER/src/gd32f4xx_it.c", "../USER/src/main.c", "../USER/src/systick.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "cd .\\..\\MDK && mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "./keil5_disp_size_bar.exe", "command": "cd .\\..\\MDK && ./keil5_disp_size_bar.exe", "disable": false, "abortAfterFailed": true}, {"name": "axf to elf", "command": "axf2elf -d \"D:\\keil-MDK\\Keil_MDK\\ARM\\ARMCC\" -i \"${outDir}\\Project.axf\" -o \"${outDir}\\Project.elf\" > \"${outDir}\\axf2elf.log\""}], "global": {"use-microLIB": false, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "target": "cortex-m4-sp"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000", "link-scatter": ["c:/Users/<USER>/Desktop/GD32/mcu_-main-board-master/example/GD_Firmware_Template/EIDE/build/McuSTUDIO_F470VET6/Project.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "output", "workspaceFolder": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "workspaceFolderBasename": "EIDE", "OutDir": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6", "OutDirRoot": "build", "OutDirBase": "build\\McuSTUDIO_F470VET6", "ProjectName": "Project", "ConfigName": "McuSTUDIO_F470VET6", "ProjectRoot": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "ExecutableName": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\Project", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.21.3\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.0.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "ToolchainRoot": "D:\\keil-MDK\\Keil_MDK\\ARM\\ARMCC"}, "sysPaths": []}