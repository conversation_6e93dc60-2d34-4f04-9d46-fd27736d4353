File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,16.209911%,8269,96,7718,551,0,96
sdio_sdcard.o,14.055516%,7170,68,7134,0,36,32
ff.o,10.091743%,5148,6,5142,0,6,0
oled.o,7.794244%,3976,22,1242,2712,22,0
ebtn.o,4.057869%,2070,60,2070,0,0,60
btod.o,3.795185%,1936,0,1936,0,0,0
mcu_cmic_gd32f470vet6.o,3.254136%,1660,592,1640,0,20,572
sd_app.o,3.116914%,1590,1452,1574,0,16,1436
gd32f4xx_dma.o,2.611150%,1332,0,1332,0,0,0
gd25qxx.o,2.344546%,1196,0,1196,0,0,0
_printf_fp_dec.o,2.066180%,1054,0,1054,0,0,0
perf_counter.o,1.893672%,966,80,882,4,80,0
gd32f4xx_rcu.o,1.693719%,864,0,864,0,0,0
_printf_fp_hex.o,1.572179%,802,0,764,38,0,0
system_gd32f4xx.o,1.368305%,698,4,694,0,4,0
gd32f4xx_adc.o,1.282051%,654,0,654,0,0,0
gd32f4xx_usart.o,1.262448%,644,0,644,0,0,0
gd32f4xx_sdio.o,1.231083%,628,0,628,0,0,0
gd32f4xx_timer.o,1.152670%,588,0,588,0,0,0
btn_app.o,1.105622%,564,196,354,14,196,0
gd32f4xx_i2c.o,0.972320%,496,0,496,0,0,0
startup_gd32f450_470.o,0.964479%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.948796%,484,0,484,0,0,0
__printf_flags_ss_wp.o,0.801772%,409,0,392,17,0,0
bigflt0.o,0.737081%,376,0,228,148,0,0
lc_ctype_c.o,0.619462%,316,0,44,272,0,0
diskio.o,0.619462%,316,0,316,0,0,0
gd32f4xx_gpio.o,0.513605%,262,0,262,0,0,0
fz_wm.l,0.501843%,256,0,256,0,0,0
oled_app.o,0.501843%,256,0,256,0,0,0
led_app.o,0.492041%,251,7,244,0,7,0
lludivv7m.o,0.466557%,238,0,238,0,0,0
gd32f4xx_misc.o,0.423430%,216,0,216,0,0,0
gd32f4xx_dac.o,0.388144%,198,0,198,0,0,0
_printf_wctomb.o,0.384223%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.368541%,188,0,148,40,0,0
_printf_intcommon.o,0.348938%,178,0,178,0,0,0
scheduler.o,0.345017%,176,76,100,0,76,0
systick.o,0.329334%,168,4,164,0,4,0
gd32f4xx_it.o,0.329334%,168,0,168,0,0,0
usart_app.o,0.311691%,159,515,156,0,3,512
perfc_port_default.o,0.301890%,154,0,154,0,0,0
fnaninf.o,0.274445%,140,0,140,0,0,0
rt_memcpy_v6.o,0.270525%,138,0,138,0,0,0
lludiv10.o,0.270525%,138,0,138,0,0,0
strcmpv7m.o,0.250921%,128,0,128,0,0,0
_printf_fp_infnan.o,0.250921%,128,0,128,0,0,0
_printf_longlong_dec.o,0.243080%,124,0,124,0,0,0
_printf_dec.o,0.235239%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.219556%,112,0,112,0,0,0
gd32f4xx_spi.o,0.203874%,104,0,104,0,0,0
rt_memcpy_w.o,0.196032%,100,0,100,0,0,0
__dczerorl2.o,0.176429%,90,0,90,0,0,0
memcmp.o,0.172508%,88,0,88,0,0,0
f2d.o,0.168588%,86,0,86,0,0,0
main.o,0.168588%,86,0,86,0,0,0
_printf_str.o,0.160746%,82,0,82,0,0,0
rt_memclr_w.o,0.152905%,78,0,78,0,0,0
_printf_pad.o,0.152905%,78,0,78,0,0,0
sys_stackheap_outer.o,0.145064%,74,0,74,0,0,0
llsdiv.o,0.141143%,72,0,72,0,0,0
lc_numeric_c.o,0.141143%,72,0,44,28,0,0
rt_memclr.o,0.133302%,68,0,68,0,0,0
_wcrtomb.o,0.125461%,64,0,64,0,0,0
strlen.o,0.121540%,62,0,62,0,0,0
rtc_app.o,0.117619%,60,0,60,0,0,0
vsnprintf.o,0.101937%,52,0,52,0,0,0
__scatter.o,0.101937%,52,0,52,0,0,0
m_wm.l,0.094096%,48,0,48,0,0,0
fpclassify.o,0.094096%,48,0,48,0,0,0
_printf_char_common.o,0.094096%,48,0,48,0,0,0
_printf_wchar.o,0.086254%,44,0,44,0,0,0
_printf_char.o,0.086254%,44,0,44,0,0,0
__2sprintf.o,0.086254%,44,0,44,0,0,0
_printf_charcount.o,0.078413%,40,0,40,0,0,0
libinit2.o,0.074492%,38,0,38,0,0,0
init_aeabi.o,0.070572%,36,0,36,0,0,0
_printf_truncate.o,0.070572%,36,0,36,0,0,0
systick_wrapper_ual.o,0.062730%,32,0,32,0,0,0
__scatter_zi.o,0.054889%,28,0,28,0,0,0
gd32f4xx_pmu.o,0.039206%,20,0,20,0,0,0
adc_app.o,0.039206%,20,0,20,0,0,0
exit.o,0.035286%,18,0,18,0,0,0
rt_ctype_table.o,0.031365%,16,0,16,0,0,0
_snputc.o,0.031365%,16,0,16,0,0,0
__printf_wp.o,0.027445%,14,0,14,0,0,0
dretinf.o,0.023524%,12,0,12,0,0,0
sys_exit.o,0.023524%,12,0,12,0,0,0
__rtentry2.o,0.023524%,12,0,12,0,0,0
fpinit.o,0.019603%,10,0,10,0,0,0
rtexit2.o,0.019603%,10,0,10,0,0,0
_sputc.o,0.019603%,10,0,10,0,0,0
_printf_ll.o,0.019603%,10,0,10,0,0,0
_printf_l.o,0.019603%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.015683%,8,0,8,0,0,0
libspace.o,0.015683%,8,96,8,0,0,96
__main.o,0.015683%,8,0,8,0,0,0
heapauxi.o,0.011762%,6,0,6,0,0,0
_printf_x.o,0.011762%,6,0,6,0,0,0
_printf_u.o,0.011762%,6,0,6,0,0,0
_printf_s.o,0.011762%,6,0,6,0,0,0
_printf_p.o,0.011762%,6,0,6,0,0,0
_printf_o.o,0.011762%,6,0,6,0,0,0
_printf_n.o,0.011762%,6,0,6,0,0,0
_printf_ls.o,0.011762%,6,0,6,0,0,0
_printf_llx.o,0.011762%,6,0,6,0,0,0
_printf_llu.o,0.011762%,6,0,6,0,0,0
_printf_llo.o,0.011762%,6,0,6,0,0,0
_printf_lli.o,0.011762%,6,0,6,0,0,0
_printf_lld.o,0.011762%,6,0,6,0,0,0
_printf_lc.o,0.011762%,6,0,6,0,0,0
_printf_i.o,0.011762%,6,0,6,0,0,0
_printf_g.o,0.011762%,6,0,6,0,0,0
_printf_f.o,0.011762%,6,0,6,0,0,0
_printf_e.o,0.011762%,6,0,6,0,0,0
_printf_d.o,0.011762%,6,0,6,0,0,0
_printf_c.o,0.011762%,6,0,6,0,0,0
_printf_a.o,0.011762%,6,0,6,0,0,0
__rtentry4.o,0.011762%,6,0,6,0,0,0
printf2.o,0.007841%,4,0,4,0,0,0
printf1.o,0.007841%,4,0,4,0,0,0
_printf_percent_end.o,0.007841%,4,0,4,0,0,0
use_no_semi.o,0.003921%,2,0,2,0,0,0
rtexit.o,0.003921%,2,0,2,0,0,0
libshutdown2.o,0.003921%,2,0,2,0,0,0
libshutdown.o,0.003921%,2,0,2,0,0,0
libinit.o,0.003921%,2,0,2,0,0,0
