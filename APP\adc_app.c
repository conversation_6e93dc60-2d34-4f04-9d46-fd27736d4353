#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];
extern uint16_t convertarr[CONVERT_NUM];

// 数据采集相关变量
static adc_collect_state_t collect_state = ADC_COLLECT_IDLE;
static uint32_t collect_start_timestamp = 0;
static uint32_t last_collect_time = 0;
static char current_filename[32] = {0};
static FIL data_file;
static uint32_t data_count = 0;

void adc_task(void)
{
    convertarr[0] = adc_value[0];

    // 如果处于采集状态，按100ms间隔采集数据
    if (collect_state == ADC_COLLECT_RUNNING) {
        uint32_t current_time = get_system_ms();

        // 检查是否到了采集时间（100ms间隔）
        if (current_time - last_collect_time >= 100) {
            last_collect_time = current_time;

            // 获取当前时间戳字符串
            char timestamp_str[16];
            uint32_t current_timestamp = rtc_get_timestamp();
            rtc_timestamp_to_string(current_timestamp, timestamp_str, sizeof(timestamp_str));

            // 准备数据字符串：数据-时间戳
            char data_line[64];
            snprintf(data_line, sizeof(data_line), "%d-%s\r\n", adc_value[0], timestamp_str);

            // 写入文件
            UINT bytes_written;
            FRESULT result = f_write(&data_file, data_line, strlen(data_line), &bytes_written);

            if (result == FR_OK) {
                data_count++;
                // 立即同步到存储设备
                f_sync(&data_file);
            }
        }
    }
}

/*!
    \brief      开始ADC数据采集
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adc_start_collect(void)
{
    if (collect_state != ADC_COLLECT_IDLE) {
        return; // 已经在采集中，不重复启动
    }

    // 获取开始时间戳并生成文件名
    collect_start_timestamp = rtc_get_timestamp();
    snprintf(current_filename, sizeof(current_filename), "0:/data_%lu.txt", collect_start_timestamp);

    // 尝试创建并打开文件
    FRESULT result = f_open(&data_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);

    if (result == FR_OK) {
        collect_state = ADC_COLLECT_RUNNING;
        last_collect_time = get_system_ms();
        data_count = 0;

        // 写入文件头信息
        char header[64];
        char timestamp_str[16];
        rtc_timestamp_to_string(collect_start_timestamp, timestamp_str, sizeof(timestamp_str));
        snprintf(header, sizeof(header), "# ADC Data Collection Started at %s\r\n", timestamp_str);

        UINT bytes_written;
        f_write(&data_file, header, strlen(header), &bytes_written);
        f_sync(&data_file);

        my_printf(DEBUG_USART, "ADC data collection started. File: %s\r\n", current_filename);
    } else {
        my_printf(DEBUG_USART, "Failed to create data file. Error: %d\r\n", result);
    }
}

/*!
    \brief      停止ADC数据采集
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adc_stop_collect(void)
{
    if (collect_state != ADC_COLLECT_RUNNING) {
        return; // 没有在采集中
    }

    // 写入文件尾信息
    char footer[64];
    char timestamp_str[16];
    uint32_t stop_timestamp = rtc_get_timestamp();
    rtc_timestamp_to_string(stop_timestamp, timestamp_str, sizeof(timestamp_str));
    snprintf(footer, sizeof(footer), "# ADC Data Collection Stopped at %s\r\n", timestamp_str);

    UINT bytes_written;
    f_write(&data_file, footer, strlen(footer), &bytes_written);

    // 关闭文件
    f_close(&data_file);

    collect_state = ADC_COLLECT_STOPPED;

    my_printf(DEBUG_USART, "ADC data collection stopped. Total samples: %lu\r\n", data_count);
}

/*!
    \brief      打印数据文件信息和内容
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adc_print_file_info(void)
{
    if (collect_state != ADC_COLLECT_STOPPED) {
        my_printf(DEBUG_USART, "No data file available. Please start and stop collection first.\r\n");
        return;
    }

    // 获取文件信息
    FILINFO file_info;
    FRESULT result = f_stat(current_filename, &file_info);

    if (result != FR_OK) {
        my_printf(DEBUG_USART, "Failed to get file info. Error: %d\r\n", result);
        return;
    }

    // 打印文件大小
    my_printf(DEBUG_USART, "\r\n=== Data File Information ===\r\n");
    my_printf(DEBUG_USART, "File: %s\r\n", current_filename);
    my_printf(DEBUG_USART, "Size: %lu bytes\r\n", file_info.fsize);
    my_printf(DEBUG_USART, "Total samples: %lu\r\n", data_count);

    // 打开文件读取内容
    FIL read_file;
    result = f_open(&read_file, current_filename, FA_READ);

    if (result != FR_OK) {
        my_printf(DEBUG_USART, "Failed to open file for reading. Error: %d\r\n", result);
        return;
    }

    my_printf(DEBUG_USART, "\r\n=== File Content ===\r\n");

    // 逐行读取并打印文件内容
    char line_buffer[128];
    UINT bytes_read;
    uint32_t line_count = 0;

    while (f_gets(line_buffer, sizeof(line_buffer), &read_file) != NULL) {
        line_count++;
        my_printf(DEBUG_USART, "%s", line_buffer);

        // 如果行数太多，可以限制显示行数
        if (line_count > 50) {
            my_printf(DEBUG_USART, "... (file too long, showing first 50 lines)\r\n");
            break;
        }
    }

    f_close(&read_file);
    my_printf(DEBUG_USART, "\r\n=== End of File ===\r\n");
}

/*!
    \brief      获取当前采集状态
    \param[in]  none
    \param[out] none
    \retval     current collection state
*/
adc_collect_state_t adc_get_collect_state(void)
{
    return collect_state;
}

