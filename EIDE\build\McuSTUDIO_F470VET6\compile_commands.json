[{"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\adc_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\adc_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\adc_app.d .\\..\\APP\\adc_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\btn_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\btn_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\btn_app.d .\\..\\APP\\btn_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\led_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\led_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\led_app.d .\\..\\APP\\led_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\oled_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\oled_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\oled_app.d .\\..\\APP\\oled_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\rtc_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\rtc_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\rtc_app.d .\\..\\APP\\rtc_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\scheduler.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\scheduler.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\scheduler.d .\\..\\APP\\scheduler.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\sd_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\sd_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\sd_app.d .\\..\\APP\\sd_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\usart_app.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\usart_app.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\usart_app.d .\\..\\APP\\usart_app.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\bsp\\mcu_cmic_gd32f470vet6.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\bsp\\mcu_cmic_gd32f470vet6.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\bsp\\mcu_cmic_gd32f470vet6.d .\\..\\Components\\bsp\\mcu_cmic_gd32f470vet6.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\ebtn\\ebtn.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\ebtn\\ebtn.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\ebtn\\ebtn.d .\\..\\Components\\ebtn\\ebtn.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\fatfs\\diskio.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\fatfs\\diskio.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\fatfs\\diskio.d .\\..\\Components\\fatfs\\diskio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\fatfs\\ff.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\fatfs\\ff.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\fatfs\\ff.d .\\..\\Components\\fatfs\\ff.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\gd25qxx.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\gd25qxx.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\gd25qxx.d .\\..\\Components\\gd25qxx\\gd25qxx.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\lfs.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs.d .\\..\\Components\\gd25qxx\\lfs.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\lfs_port.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs_port.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs_port.d .\\..\\Components\\gd25qxx\\lfs_port.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\lfs_util.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs_util.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs_util.d .\\..\\Components\\gd25qxx\\lfs_util.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\oled\\oled.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\oled\\oled.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\oled\\oled.d .\\..\\Components\\oled\\oled.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\sdio\\sdio_sdcard.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\sdio\\sdio_sdcard.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\sdio\\sdio_sdcard.d .\\..\\Components\\sdio\\sdio_sdcard.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.d .\\..\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_adc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_adc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_adc.d .\\..\\Libraries\\Source\\gd32f4xx_adc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_can.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_can.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_can.d .\\..\\Libraries\\Source\\gd32f4xx_can.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_crc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_crc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_crc.d .\\..\\Libraries\\Source\\gd32f4xx_crc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_ctc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_ctc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_ctc.d .\\..\\Libraries\\Source\\gd32f4xx_ctc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dac.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dac.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dac.d .\\..\\Libraries\\Source\\gd32f4xx_dac.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dbg.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dbg.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dbg.d .\\..\\Libraries\\Source\\gd32f4xx_dbg.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dci.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dci.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dci.d .\\..\\Libraries\\Source\\gd32f4xx_dci.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dma.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dma.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dma.d .\\..\\Libraries\\Source\\gd32f4xx_dma.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_enet.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_enet.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_enet.d .\\..\\Libraries\\Source\\gd32f4xx_enet.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_exmc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_exmc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_exmc.d .\\..\\Libraries\\Source\\gd32f4xx_exmc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_exti.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_exti.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_exti.d .\\..\\Libraries\\Source\\gd32f4xx_exti.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_fmc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_fmc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_fmc.d .\\..\\Libraries\\Source\\gd32f4xx_fmc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_fwdgt.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_fwdgt.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_fwdgt.d .\\..\\Libraries\\Source\\gd32f4xx_fwdgt.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_gpio.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_gpio.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_gpio.d .\\..\\Libraries\\Source\\gd32f4xx_gpio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_i2c.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_i2c.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_i2c.d .\\..\\Libraries\\Source\\gd32f4xx_i2c.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_ipa.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_ipa.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_ipa.d .\\..\\Libraries\\Source\\gd32f4xx_ipa.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_iref.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_iref.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_iref.d .\\..\\Libraries\\Source\\gd32f4xx_iref.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_misc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_misc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_misc.d .\\..\\Libraries\\Source\\gd32f4xx_misc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_pmu.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_pmu.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_pmu.d .\\..\\Libraries\\Source\\gd32f4xx_pmu.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_rcu.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_rcu.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_rcu.d .\\..\\Libraries\\Source\\gd32f4xx_rcu.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_rtc.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_rtc.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_rtc.d .\\..\\Libraries\\Source\\gd32f4xx_rtc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_sdio.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_sdio.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_sdio.d .\\..\\Libraries\\Source\\gd32f4xx_sdio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_spi.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_spi.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_spi.d .\\..\\Libraries\\Source\\gd32f4xx_spi.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_syscfg.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_syscfg.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_syscfg.d .\\..\\Libraries\\Source\\gd32f4xx_syscfg.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_timer.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_timer.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_timer.d .\\..\\Libraries\\Source\\gd32f4xx_timer.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_tli.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_tli.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_tli.d .\\..\\Libraries\\Source\\gd32f4xx_tli.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_trng.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_trng.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_trng.d .\\..\\Libraries\\Source\\gd32f4xx_trng.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_usart.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_usart.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_usart.d .\\..\\Libraries\\Source\\gd32f4xx_usart.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_wwdgt.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_wwdgt.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_wwdgt.d .\\..\\Libraries\\Source\\gd32f4xx_wwdgt.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\startup_gd32f450_470.s", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 --cpu Cortex-M4.fp --li -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\startup_gd32f450_470.o --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\startup_gd32f450_470.d .\\..\\Libraries\\startup_gd32f450_470.s"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\USER\\src\\gd32f4xx_it.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\gd32f4xx_it.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\gd32f4xx_it.d .\\..\\USER\\src\\gd32f4xx_it.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\USER\\src\\main.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\main.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\main.d .\\..\\USER\\src\\main.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE", "file": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\USER\\src\\systick.c", "command": "\"D:\\keil-<PERSON><PERSON>\\Keil_MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Libraries/Include -I../USER/inc -I../Driver/CMSIS/GD/GD32F4xx/Include -I../Components/bsp -I../Components/oled -I../Components/gd25qxx -I../Components/ebtn -I../Components/sdio -I../Components/fatfs -I../APP -I.cmsis/include -I../MDK/RTE/_McuSTUDIO_F470VET6 -DUSE_STDPERIPH_DRIVER -DGD32F470 --cpu Cortex-M4.fp --li --c99 -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\systick.o --no_depend_system_headers --depend .\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\systick.d .\\..\\USER\\src\\systick.c"}]