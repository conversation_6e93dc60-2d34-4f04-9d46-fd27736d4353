# ADC数据采集功能说明

## 功能概述

本功能实现了基于按键控制的ADC数据采集系统，具有以下特性：

- **按键1 (USER_BUTTON_0)**：启动ADC数据采集
- **按键2 (USER_BUTTON_1)**：停止采集并打印文件信息
- **数据格式**：`ADC值-时间戳`
- **采集间隔**：100毫秒
- **文件名格式**：`data_时间戳.txt`

## 实现细节

### 1. 文件结构修改

#### APP/adc_app.h
- 添加了数据采集状态枚举 `adc_collect_state_t`
- 新增函数声明：
  - `adc_start_collect()` - 开始数据采集
  - `adc_stop_collect()` - 停止数据采集
  - `adc_print_file_info()` - 打印文件信息和内容
  - `adc_get_collect_state()` - 获取当前采集状态

#### APP/adc_app.c
- 扩展了原有的 `adc_task()` 函数，增加了100ms间隔的数据采集逻辑
- 实现了完整的文件操作功能，包括创建、写入、读取和状态管理

#### APP/rtc_app.h & APP/rtc_app.c
- 新增 `rtc_get_timestamp()` - 获取当前时间戳
- 新增 `rtc_timestamp_to_string()` - 将时间戳转换为字符串格式

#### APP/btn_app.c
- 修改了按键1和按键2的事件处理逻辑
- 添加了LED状态指示：
  - LED1亮：正在采集数据
  - LED1灭：停止采集
  - LED2亮：数据可查看

### 2. 工作流程

1. **启动采集**：
   - 按下按键1
   - 系统获取当前时间戳作为文件名
   - 创建数据文件 `0:/data_时间戳.txt`
   - 开始100ms间隔的ADC数据采集
   - LED1点亮表示正在采集

2. **数据采集**：
   - 每100ms读取一次ADC值
   - 获取当前时间戳
   - 将数据按格式 `ADC值-时间戳` 写入文件
   - 立即同步到存储设备

3. **停止采集**：
   - 按下按键2
   - 停止数据采集并关闭文件
   - LED1熄灭，LED2点亮
   - 通过串口打印文件信息和内容

### 3. 数据文件格式

```
# ADC Data Collection Started at HH:MM:SS
ADC值1-HH:MM:SS
ADC值2-HH:MM:SS
ADC值3-HH:MM:SS
...
# ADC Data Collection Stopped at HH:MM:SS
```

### 4. 串口输出示例

```
ADC data collection started. File: 0:/data_12345.txt
ADC data collection stopped. Total samples: 50

=== Data File Information ===
File: 0:/data_12345.txt
Size: 1024 bytes
Total samples: 50

=== File Content ===
# ADC Data Collection Started at 10:30:15
2048-10:30:15
2051-10:30:15
2049-10:30:16
...
=== End of File ===
```

## 使用说明

1. 确保SD卡已正确插入并初始化
2. 按下按键1开始数据采集（LED1亮起）
3. 等待一段时间让系统采集数据
4. 按下按键2停止采集并查看结果（LED1熄灭，LED2亮起）
5. 通过串口查看文件大小和数据内容

## 注意事项

- 系统使用FATFS文件系统，需要SD卡支持
- 时间戳基于RTC，格式为 HH:MM:SS
- 文件名使用时间戳确保唯一性
- 数据采集过程中会实时同步到存储设备
- 如果文件内容过长，串口输出会限制在前50行

## 技术特点

- **非阻塞设计**：数据采集在任务调度器中运行，不影响其他功能
- **实时存储**：每次数据写入后立即同步，确保数据安全
- **状态管理**：完整的状态机管理采集过程
- **错误处理**：包含文件操作错误检查和用户提示
- **LED指示**：直观的状态指示灯
