{"c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\adc_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\adc_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\btn_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\btn_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\led_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\led_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\oled_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\oled_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\rtc_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\rtc_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\scheduler.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\scheduler.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\sd_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\sd_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\APP\\usart_app.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\APP\\usart_app.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\bsp\\mcu_cmic_gd32f470vet6.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\bsp\\mcu_cmic_gd32f470vet6.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\ebtn\\ebtn.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\ebtn\\ebtn.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\fatfs\\diskio.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\fatfs\\diskio.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\fatfs\\ff.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\fatfs\\ff.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\gd25qxx.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\gd25qxx.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\lfs.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\lfs_port.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs_port.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\gd25qxx\\lfs_util.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\gd25qxx\\lfs_util.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\oled\\oled.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\oled\\oled.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Components\\sdio\\sdio_sdcard.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Components\\sdio\\sdio_sdcard.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_adc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_adc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_can.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_can.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_crc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_crc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_ctc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_ctc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dac.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dac.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dbg.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dbg.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dci.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dci.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_dma.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_dma.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_enet.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_enet.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_exmc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_exmc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_exti.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_exti.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_fmc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_fmc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_fwdgt.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_fwdgt.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_gpio.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_gpio.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_i2c.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_i2c.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_ipa.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_ipa.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_iref.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_iref.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_misc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_misc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_pmu.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_pmu.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_rcu.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_rcu.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_rtc.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_rtc.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_sdio.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_sdio.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_spi.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_spi.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_syscfg.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_syscfg.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_timer.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_timer.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_tli.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_tli.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_trng.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_trng.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_usart.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_usart.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\Source\\gd32f4xx_wwdgt.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\Source\\gd32f4xx_wwdgt.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\Libraries\\startup_gd32f450_470.s": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\Libraries\\startup_gd32f450_470.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\USER\\src\\gd32f4xx_it.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\gd32f4xx_it.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\USER\\src\\main.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\main.o", "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\USER\\src\\systick.c": "c:\\Users\\<USER>\\Desktop\\GD32\\mcu_-main-board-master\\example\\GD_Firmware_Template\\EIDE\\build\\McuSTUDIO_F470VET6\\.obj\\__\\USER\\src\\systick.o"}